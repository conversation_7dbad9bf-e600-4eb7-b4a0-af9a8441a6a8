-- Email-System 数据库设计
-- 注：模板与版本表由 Email 模块维护；本系统不再维护多语言映射
-- 发件渠道相关表已移除；发件账户信息通过模板关联获取
-- 统一发送流程：活动/自动化/手动任务使用统一的发送引擎

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 多语言表已废弃：由用户自行创建不同语言的模板记录进行区分

-- ----------------------------
-- Table structure for sending_tasks
-- ----------------------------
CREATE TABLE IF NOT EXISTS sending_tasks (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  task_type       VARCHAR(32) NOT NULL COMMENT '任务类型（campaign/journey/manual）',
  source_id       BIGINT NOT NULL COMMENT '源对象ID（campaign_id/journey_id/manual_task_id）',
  name            VARCHAR(128) NOT NULL COMMENT '任务名称',
  template_id     BIGINT NOT NULL COMMENT '模板ID（关联发件账户）',
  audience_json   JSON NOT NULL COMMENT '受众配置',
  settings_json   JSON NOT NULL COMMENT '发送设置',
  schedule_time   DATETIME NULL COMMENT '计划时间',
  timezone        VARCHAR(64) NULL COMMENT '时区',
  automation_config JSON NULL COMMENT '自动化配置',
  status          VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态',
  created_by      BIGINT NULL COMMENT '创建人',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_task_type_source (tenant_id, task_type, source_id),
  KEY idx_task_status (tenant_id, status, schedule_time),
  KEY idx_task_template (tenant_id, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一发送任务表';

-- ----------------------------
-- Table structure for sending_batches
-- ----------------------------
CREATE TABLE IF NOT EXISTS sending_batches (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  task_id         BIGINT NOT NULL COMMENT '发送任务ID',
  batch_no        INT NOT NULL COMMENT '批次号',
  size_planned    INT NOT NULL COMMENT '计划发送数量',
  size_sent       INT NOT NULL DEFAULT 0 COMMENT '实际发送数量',
  status          VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态',
  scheduled_at    DATETIME NULL COMMENT '计划执行时间',
  started_at      DATETIME NULL COMMENT '开始时间',
  finished_at     DATETIME NULL COMMENT '完成时间',
  error_message   VARCHAR(1024) NULL COMMENT '错误信息',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_batch_task (tenant_id, task_id, batch_no),
  KEY idx_batch_status (tenant_id, status, scheduled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送批次表';

-- ----------------------------
-- Table structure for campaigns
-- ----------------------------
CREATE TABLE IF NOT EXISTS campaigns (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  name            VARCHAR(128) NOT NULL COMMENT '活动名称',
  type            VARCHAR(32)  NOT NULL DEFAULT 'scheduled' COMMENT '活动类型（scheduled/triggered/recurring）',
  template_id     BIGINT NOT NULL COMMENT '模板ID（Email 模块模板ID，模板关联发件账户）',
  audience_json   JSON NOT NULL COMMENT '受众配置（包含/排除 标签）',
  settings_json   JSON NOT NULL COMMENT '内容与追踪设置（subject/from/utm/track/headers）',
  schedule_time   DATETIME NULL COMMENT '计划时间',
  timezone        VARCHAR(64) NULL COMMENT '时区',
  ab_enabled      TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用A/B',
  ab_experiment_id BIGINT NULL COMMENT 'A/B 实验ID',
  automation_config JSON NULL COMMENT '自动化配置（可接入自动化流程）',
  status          VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态（draft/scheduled/running/paused/canceled/completed）',
  created_by      BIGINT NULL COMMENT '创建人',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_campaign (tenant_id, name),
  KEY idx_campaign_status (tenant_id, status, schedule_time),
  KEY idx_campaign_template (tenant_id, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- ----------------------------
-- Table structure for journeys
-- ----------------------------
CREATE TABLE IF NOT EXISTS journeys (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  name            VARCHAR(128) NOT NULL,
  journey_type    VARCHAR(32) NOT NULL COMMENT 'welcome/onboarding/re_engagement/abandoned_cart/custom',
  template_id     BIGINT NULL COMMENT '默认模板ID（模板关联发件账户）',
  status          VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT 'draft/published/paused/stopped',
  current_version BIGINT NULL,
  entry_frequency VARCHAR(32) NOT NULL DEFAULT 'once' COMMENT 'once/repeat/always',
  rate_limit_config JSON NULL COMMENT '上限控制配置',
  target_audience_filter JSON NULL COMMENT '目标受众过滤（标签配置）',
  timezone_handling VARCHAR(32) NOT NULL DEFAULT 'user_timezone' COMMENT 'user_timezone/business_hours/optimal_hours',
  exit_conditions JSON NULL COMMENT '退出条件配置',
  created_by      BIGINT NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_journey (tenant_id, name),
  KEY idx_journey_template (tenant_id, template_id),
  KEY idx_journey_type (tenant_id, journey_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动化旅程表';

-- ----------------------------
-- Table structure for manual_send_tasks
-- ----------------------------
CREATE TABLE IF NOT EXISTS manual_send_tasks (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  name            VARCHAR(128) NOT NULL COMMENT '任务名称',
  template_id     BIGINT NOT NULL COMMENT '模板ID（关联发件账户）',
  audience_json   JSON NOT NULL COMMENT '受众配置（包含/排除 标签）',
  settings_json   JSON NOT NULL COMMENT '发送设置（subject/from/utm/track/headers）',
  schedule_time   DATETIME NULL COMMENT '计划时间',
  timezone        VARCHAR(64) NULL COMMENT '时区',
  automation_config JSON NULL COMMENT '自动化配置（可接入自动化流程）',
  status          VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态（draft/scheduled/running/paused/canceled/completed）',
  created_by      BIGINT NULL COMMENT '创建人',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_manual_task (tenant_id, name),
  KEY idx_manual_task_status (tenant_id, status, schedule_time),
  KEY idx_manual_task_template (tenant_id, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手动发送任务';

-- ----------------------------
-- Table structure for email_messages
-- ----------------------------
CREATE TABLE IF NOT EXISTS email_messages (
  id                  BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id           BIGINT NOT NULL COMMENT '租户ID',
  campaign_id         BIGINT NULL COMMENT '活动ID',
  campaign_variant_id BIGINT NULL COMMENT '活动变体ID',
  journey_id          BIGINT NULL COMMENT '旅程ID',
  journey_node_id     BIGINT NULL COMMENT '旅程节点ID',
  manual_task_id      BIGINT NULL COMMENT '手动任务ID',
  message_type        VARCHAR(16) NOT NULL DEFAULT 'email' COMMENT '消息类型',
  contact_id          BIGINT NULL COMMENT '联系人ID',
  to_email            VARCHAR(255) NOT NULL COMMENT '收件人邮箱',
  from_email          VARCHAR(255) NOT NULL COMMENT '发件人邮箱（来源于 email 模块账户配置）',
  from_name           VARCHAR(128) NULL COMMENT '发件人名称',
  subject             VARCHAR(512) NOT NULL COMMENT '主题',
  -- 不再保存正文以降低存储成本，通过模板+变量复原
  headers_json        JSON NULL COMMENT '自定义头部',
  personalization_json JSON NULL COMMENT '个性化变量（渲染所需变量值）',
  tags_json           JSON NULL COMMENT '标签',
  track_open          TINYINT(1) NOT NULL DEFAULT 1 COMMENT '打开追踪',
  track_click         TINYINT(1) NOT NULL DEFAULT 1 COMMENT '点击追踪',
  priority            INT NOT NULL DEFAULT 100 COMMENT '优先级',
  scheduled_at        DATETIME NULL COMMENT '计划发送时间',
  status              VARCHAR(32) NOT NULL DEFAULT 'queued' COMMENT '状态（queued/sending/sent/failed/skipped/suppressed）',
  last_error          VARCHAR(1024) NULL COMMENT '最后错误',
  provider_message_id VARCHAR(255) NULL COMMENT 'ESP 消息ID',
  dedupe_key          VARCHAR(128) NULL COMMENT '幂等键',
  lock_token          VARCHAR(64) NULL COMMENT '执行锁标识',
  locked_at           DATETIME NULL COMMENT '锁定时间',
  attempts            INT NOT NULL DEFAULT 0 COMMENT '尝试次数',
  next_attempt_at     DATETIME NULL COMMENT '下次重试时间',
  sent_at             DATETIME NULL COMMENT '发送时间',
  delivered_at        DATETIME NULL COMMENT '投递时间',
  created_at          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_msg_status (tenant_id, status, scheduled_at),
  KEY idx_msg_campaign (tenant_id, campaign_id),
  KEY idx_msg_journey (tenant_id, journey_id),
  KEY idx_msg_manual (tenant_id, manual_task_id),
  KEY idx_msg_contact (tenant_id, contact_id),
  KEY idx_msg_to_email (tenant_id, to_email, created_at),
  KEY idx_msg_dedupe (dedupe_key),
  KEY idx_msg_lock (lock_token, locked_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送队列表';

-- 多语言已移除，无需维护语言变体迁移

SET FOREIGN_KEY_CHECKS = 1;
