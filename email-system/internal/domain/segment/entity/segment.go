package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

// Segment 人群圈选实体
type Segment struct {
	ID            int64         `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID      int64         `json:"tenant_id" gorm:"not null;index:idx_segment_tenant"`
	Name          string        `json:"name" gorm:"not null;size:128"`
	TagID         int64         `json:"tag_id" gorm:"not null;index:idx_segment_tag"`
	RuleJSON      string        `json:"rule_json" gorm:"not null;type:json"`
	Type          SegmentType   `json:"type" gorm:"not null;size:32"`
	RefreshPolicy *string       `json:"refresh_policy" gorm:"size:64"`
	Status        SegmentStatus `json:"status" gorm:"not null;default:'active';size:32"`
	CreatedAt     time.Time     `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time     `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// SegmentType 人群圈选类型
type SegmentType string

const (
	SegmentTypeDynamic SegmentType = "dynamic" // 动态圈选
	SegmentTypeStatic  SegmentType = "static"  // 静态圈选
)

// IsValid 检查人群圈选类型是否有效
func (t SegmentType) IsValid() bool {
	switch t {
	case SegmentTypeDynamic, SegmentTypeStatic:
		return true
	default:
		return false
	}
}

// SegmentStatus 人群圈选状态
type SegmentStatus string

const (
	SegmentStatusActive   SegmentStatus = "active"   // 活跃
	SegmentStatusInactive SegmentStatus = "inactive" // 非活跃
	SegmentStatusArchived SegmentStatus = "archived" // 已归档
)

// IsValid 检查人群圈选状态是否有效
func (s SegmentStatus) IsValid() bool {
	switch s {
	case SegmentStatusActive, SegmentStatusInactive, SegmentStatusArchived:
		return true
	default:
		return false
	}
}

// RefreshPolicy 刷新策略
type RefreshPolicy string

const (
	RefreshPolicySchedule RefreshPolicy = "schedule" // 定时刷新
	RefreshPolicyTrigger  RefreshPolicy = "trigger"  // 触发刷新
	RefreshPolicyOnce     RefreshPolicy = "once"     // 一次性
)

// IsValidRefreshPolicy 检查刷新策略是否有效
func IsValidRefreshPolicy(policy RefreshPolicy) bool {
	switch policy {
	case RefreshPolicySchedule, RefreshPolicyTrigger, RefreshPolicyOnce:
		return true
	default:
		return false
	}
}

// TableName 指定表名
func (Segment) TableName() string {
	return "segments"
}

// GetRule 获取规则
func (s *Segment) GetRule() (map[string]interface{}, error) {
	if s.RuleJSON == "" {
		return nil, nil
	}

	var rule map[string]interface{}
	if err := json.Unmarshal([]byte(s.RuleJSON), &rule); err != nil {
		return nil, err
	}

	return rule, nil
}

// SetRule 设置规则
func (s *Segment) SetRule(rule map[string]interface{}) error {
	if rule == nil {
		s.RuleJSON = "{}"
		return nil
	}

	data, err := json.Marshal(rule)
	if err != nil {
		return err
	}

	s.RuleJSON = string(data)
	return nil
}

// IsDynamic 是否为动态圈选
func (s *Segment) IsDynamic() bool {
	return s.Type == SegmentTypeDynamic
}

// IsStatic 是否为静态圈选
func (s *Segment) IsStatic() bool {
	return s.Type == SegmentTypeStatic
}

// CanRefresh 是否可以刷新
func (s *Segment) CanRefresh() bool {
	return s.IsDynamic() && s.RefreshPolicy != nil && *s.RefreshPolicy != string(RefreshPolicyOnce)
}

// IsActive 是否为活跃状态
func (s *Segment) IsActive() bool {
	return s.Status == SegmentStatusActive
}

// Clone 克隆人群圈选实体
func (s *Segment) Clone() *Segment {
	clone := &Segment{
		ID:        s.ID,
		TenantID:  s.TenantID,
		Name:      s.Name,
		TagID:     s.TagID,
		RuleJSON:  s.RuleJSON,
		Type:      s.Type,
		Status:    s.Status,
		CreatedAt: s.CreatedAt,
		UpdatedAt: s.UpdatedAt,
	}

	if s.RefreshPolicy != nil {
		refreshPolicy := *s.RefreshPolicy
		clone.RefreshPolicy = &refreshPolicy
	}

	return clone
}

// ToJSON 转换为JSON字符串
func (s *Segment) ToJSON() (string, error) {
	data, err := json.Marshal(s)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// Validate 验证人群圈选数据
func (s *Segment) Validate() error {
	if s.TenantID <= 0 {
		return NewSegmentValidationError("tenant_id", "租户ID不能为空")
	}

	if s.Name == "" {
		return NewSegmentValidationError("name", "人群圈选名称不能为空")
	}

	if len(s.Name) > 128 {
		return NewSegmentValidationError("name", "人群圈选名称长度不能超过128个字符")
	}

	if s.TagID <= 0 {
		return NewSegmentValidationError("tag_id", "标签ID不能为空")
	}

	if !s.Type.IsValid() {
		return NewSegmentValidationError("type", "无效的人群圈选类型")
	}

	if !s.Status.IsValid() {
		return NewSegmentValidationError("status", "无效的人群圈选状态")
	}

	if s.RuleJSON == "" {
		return NewSegmentValidationError("rule_json", "圈选规则不能为空")
	}

	// 验证规则JSON格式
	var rule map[string]interface{}
	if err := json.Unmarshal([]byte(s.RuleJSON), &rule); err != nil {
		return NewSegmentValidationError("rule_json", "圈选规则格式无效")
	}

	// 验证刷新策略
	if s.RefreshPolicy != nil && !IsValidRefreshPolicy(RefreshPolicy(*s.RefreshPolicy)) {
		return NewSegmentValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// SegmentSnapshot 人群圈选快照
type SegmentSnapshot struct {
	ID         int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID   int64      `json:"tenant_id" gorm:"not null;index:idx_segment_snapshot_tenant"`
	SegmentID  int64      `json:"segment_id" gorm:"not null;index:idx_segment_snapshot_segment"`
	ContactIDs []int64    `json:"contact_ids" gorm:"-"`
	DataJSON   string     `json:"-" gorm:"type:longtext"`
	Size       int64      `json:"size" gorm:"not null"`
	CreatedAt  time.Time  `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	ExpiresAt  *time.Time `json:"expires_at"`
}

// TableName 指定表名
func (SegmentSnapshot) TableName() string {
	return "segment_snapshots"
}

// SetContactIDs 设置联系人ID列表
func (s *SegmentSnapshot) SetContactIDs(contactIDs []int64) error {
	s.ContactIDs = contactIDs
	s.Size = int64(len(contactIDs))

	// 序列化为JSON
	data, err := json.Marshal(contactIDs)
	if err != nil {
		return NewSegmentSnapshotInvalidError(fmt.Sprintf("serialize contact IDs: %v", err))
	}

	s.DataJSON = string(data)
	return nil
}

// GetContactIDs 获取联系人ID列表
func (s *SegmentSnapshot) GetContactIDs() ([]int64, error) {
	if s.ContactIDs != nil {
		return s.ContactIDs, nil
	}

	if s.DataJSON == "" {
		return []int64{}, nil
	}

	var contactIDs []int64
	if err := json.Unmarshal([]byte(s.DataJSON), &contactIDs); err != nil {
		return nil, NewSegmentSnapshotInvalidError(fmt.Sprintf("deserialize contact IDs: %v", err))
	}

	s.ContactIDs = contactIDs
	return contactIDs, nil
}

// IsExpired 检查快照是否过期
func (s *SegmentSnapshot) IsExpired() bool {
	if s.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*s.ExpiresAt)
}

// SetExpiration 设置过期时间
func (s *SegmentSnapshot) SetExpiration(duration time.Duration) {
	expiresAt := time.Now().Add(duration)
	s.ExpiresAt = &expiresAt
}

// SegmentJob 人群圈选任务实体
type SegmentJob struct {
	ID         int64            `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID   int64            `json:"tenant_id" gorm:"not null;index:idx_segment_job_tenant"`
	SegmentID  int64            `json:"segment_id" gorm:"not null;index:idx_segment_job_segment"`
	JobType    SegmentJobType   `json:"job_type" gorm:"not null;size:32"`
	Status     SegmentJobStatus `json:"status" gorm:"not null;default:'queued';size:32"`
	Progress   int              `json:"progress" gorm:"not null;default:0"`
	ResultJSON *string          `json:"result_json" gorm:"type:json"`
	ErrorMsg   *string          `json:"error_msg" gorm:"type:text"`
	StartedAt  *time.Time       `json:"started_at"`
	FinishedAt *time.Time       `json:"finished_at"`
	CreatedAt  time.Time        `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time        `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// SegmentJobType 人群圈选任务类型
type SegmentJobType string

const (
	SegmentJobTypePreview SegmentJobType = "preview" // 预览
	SegmentJobTypeRebuild SegmentJobType = "rebuild" // 重建
)

// IsValid 检查任务类型是否有效
func (t SegmentJobType) IsValid() bool {
	switch t {
	case SegmentJobTypePreview, SegmentJobTypeRebuild:
		return true
	default:
		return false
	}
}

// SegmentJobStatus 人群圈选任务状态
type SegmentJobStatus string

const (
	SegmentJobStatusQueued    SegmentJobStatus = "queued"    // 排队中
	SegmentJobStatusRunning   SegmentJobStatus = "running"   // 运行中
	SegmentJobStatusCompleted SegmentJobStatus = "completed" // 已完成
	SegmentJobStatusFailed    SegmentJobStatus = "failed"    // 失败
	SegmentJobStatusCancelled SegmentJobStatus = "cancelled" // 已取消
)

// IsValid 检查任务状态是否有效
func (s SegmentJobStatus) IsValid() bool {
	switch s {
	case SegmentJobStatusQueued, SegmentJobStatusRunning, SegmentJobStatusCompleted, SegmentJobStatusFailed, SegmentJobStatusCancelled:
		return true
	default:
		return false
	}
}

// TableName 指定表名
func (SegmentJob) TableName() string {
	return "segment_jobs"
}

// GetResult 获取任务结果
func (j *SegmentJob) GetResult() (map[string]interface{}, error) {
	if j.ResultJSON == nil || *j.ResultJSON == "" {
		return nil, nil
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(*j.ResultJSON), &result); err != nil {
		return nil, err
	}

	return result, nil
}

// SetResult 设置任务结果
func (j *SegmentJob) SetResult(result map[string]interface{}) error {
	if result == nil {
		j.ResultJSON = nil
		return nil
	}

	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	resultJSON := string(data)
	j.ResultJSON = &resultJSON
	return nil
}

// IsCompleted 是否已完成
func (j *SegmentJob) IsCompleted() bool {
	return j.Status == SegmentJobStatusCompleted
}

// IsFailed 是否失败
func (j *SegmentJob) IsFailed() bool {
	return j.Status == SegmentJobStatusFailed
}

// IsRunning 是否运行中
func (j *SegmentJob) IsRunning() bool {
	return j.Status == SegmentJobStatusRunning
}

// Validate 验证任务数据
func (j *SegmentJob) Validate() error {
	if j.TenantID <= 0 {
		return NewSegmentValidationError("tenant_id", "租户ID不能为空")
	}

	if j.SegmentID <= 0 {
		return NewSegmentValidationError("segment_id", "人群圈选ID不能为空")
	}

	if !j.JobType.IsValid() {
		return NewSegmentValidationError("job_type", "无效的任务类型")
	}

	if !j.Status.IsValid() {
		return NewSegmentValidationError("status", "无效的任务状态")
	}

	if j.Progress < 0 || j.Progress > 100 {
		return NewSegmentValidationError("progress", "任务进度必须在0-100之间")
	}

	return nil
}
