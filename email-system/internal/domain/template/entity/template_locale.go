package entity

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/datatypes"
)

// TemplateLocale 模板多语言内容实体（Email-System内部表）
type TemplateLocale struct {
	ID         int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID   int64  `json:"tenant_id" gorm:"not null;index:idx_template_locale_tenant"`
	TemplateID int64  `json:"template_id" gorm:"not null;index:idx_template_locale_template"` // 外部Email模块的template_id
	Locale     string `json:"locale" gorm:"not null;size:16;index:idx_template_locale_unique;uniqueIndex:uk_template_locale"`
	
	// 多语言内容
	Name        string `json:"name" gorm:"not null;size:255"`
	Subject     string `json:"subject" gorm:"not null;size:512"`
	PreHeader   string `json:"pre_header" gorm:"size:255"`
	HtmlContent string `json:"html_content" gorm:"type:longtext"`
	TextContent string `json:"text_content" gorm:"type:longtext"`
	
	// 追踪配置（JSON）
	TrackingOptions datatypes.JSON `json:"tracking_options" gorm:"type:json"`
	
	// 状态与元数据
	Status      TemplateLocaleStatus `json:"status" gorm:"not null;default:'draft';size:32"`
	Version     int                  `json:"version" gorm:"not null;default:1"`
	Variables   datatypes.JSON       `json:"variables" gorm:"type:json"`
	Metadata    datatypes.JSON       `json:"metadata" gorm:"type:json"`
	
	CreatedAt time.Time `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TemplateLocaleStatus 模板多语言版本状态
type TemplateLocaleStatus string

const (
	TemplateLocaleStatusDraft     TemplateLocaleStatus = "draft"     // 草稿
	TemplateLocaleStatusActive    TemplateLocaleStatus = "active"    // 激活
	TemplateLocaleStatusInactive  TemplateLocaleStatus = "inactive"  // 停用
	TemplateLocaleStatusArchived  TemplateLocaleStatus = "archived"  // 归档
)

// IsValid 检查状态是否有效
func (s TemplateLocaleStatus) IsValid() bool {
	switch s {
	case TemplateLocaleStatusDraft, TemplateLocaleStatusActive, TemplateLocaleStatusInactive, TemplateLocaleStatusArchived:
		return true
	default:
		return false
	}
}

// TableName 指定表名
func (TemplateLocale) TableName() string {
	return "template_locales"
}

// TrackingOptions 追踪配置结构
type TrackingOptions struct {
	Enabled                bool                `json:"enabled"`                             // 是否启用追踪
	UTM                    *UTMOptions         `json:"utm,omitempty"`                       // UTM配置
	Attribution            *AttributionOptions `json:"attribution,omitempty"`              // 归因配置
	Pixel                  *PixelOptions       `json:"pixel,omitempty"`                     // 像素追踪配置
	Redirect               *RedirectOptions    `json:"redirect,omitempty"`                  // 链接重定向配置
	Domain                 *DomainOptions      `json:"domain,omitempty"`                    // 追踪域名配置
	ConversionWindowHours  int                 `json:"conversion_window_hours,omitempty"`   // 转化窗口（小时）
	LinkDomainWhitelist    []string            `json:"link_domain_whitelist,omitempty"`    // 链接域名白名单
}

// UTMOptions UTM参数配置
type UTMOptions struct {
	AutoAppend bool   `json:"auto_append"`        // 自动追加UTM参数
	Campaign   string `json:"campaign,omitempty"` // utm_campaign
	Content    string `json:"content,omitempty"`  // utm_content
	Source     string `json:"source,omitempty"`   // utm_source
	Medium     string `json:"medium,omitempty"`   // utm_medium
	Term       string `json:"term,omitempty"`     // utm_term
}

// AttributionOptions 归因配置
type AttributionOptions struct {
	WindowHours int `json:"window_hours"` // 归因窗口（小时）
}

// PixelOptions 像素追踪配置
type PixelOptions struct {
	EnablePixel  bool `json:"enable_pixel"`  // 启用像素追踪
	EnableBeacon bool `json:"enable_beacon"` // 启用Beacon追踪
	EnableAMP    bool `json:"enable_amp"`    // 启用AMP追踪
}

// RedirectOptions 重定向配置
type RedirectOptions struct {
	Whitelist []string `json:"whitelist"` // 域名白名单
}

// DomainOptions 追踪域名配置
type DomainOptions struct {
	TrackingDomain string       `json:"tracking_domain"` // 自定义追踪域名
	Status         DomainStatus `json:"status"`          // 域名状态
}

// DomainStatus 域名状态
type DomainStatus string

const (
	DomainStatusPending  DomainStatus = "pending"  // 待验证
	DomainStatusVerified DomainStatus = "verified" // 已验证
	DomainStatusFailed   DomainStatus = "failed"   // 验证失败
)

// SetTrackingOptions 设置追踪配置
func (t *TemplateLocale) SetTrackingOptions(opts *TrackingOptions) error {
	if opts == nil {
		t.TrackingOptions = nil
		return nil
	}
	
	data, err := json.Marshal(opts)
	if err != nil {
		return fmt.Errorf("marshal tracking_options: %w", err)
	}
	
	t.TrackingOptions = datatypes.JSON(data)
	return nil
}

// GetTrackingOptions 获取追踪配置
func (t *TemplateLocale) GetTrackingOptions() *TrackingOptions {
	if len(t.TrackingOptions) == 0 {
		return nil
	}
	
	var opts TrackingOptions
	if err := json.Unmarshal(t.TrackingOptions, &opts); err != nil {
		return nil
	}
	
	return &opts
}

// GetTrackingOptionsWithDefaults 获取追踪配置（包含默认值）
func (t *TemplateLocale) GetTrackingOptionsWithDefaults() *TrackingOptions {
	opts := t.GetTrackingOptions()
	if opts == nil {
		// 返回默认配置
		return &TrackingOptions{
			Enabled: true,
			UTM: &UTMOptions{
				AutoAppend: true,
				Source:     "newsletter",
				Medium:     "email",
			},
			Attribution: &AttributionOptions{
				WindowHours: 24,
			},
			Pixel: &PixelOptions{
				EnablePixel:  true,
				EnableBeacon: true,
				EnableAMP:    true,
			},
			ConversionWindowHours: 24,
		}
	}
	
	// 填充缺失的默认值
	if opts.UTM == nil {
		opts.UTM = &UTMOptions{
			AutoAppend: true,
			Source:     "newsletter",
			Medium:     "email",
		}
	}
	
	if opts.Attribution == nil {
		opts.Attribution = &AttributionOptions{
			WindowHours: 24,
		}
	}
	
	if opts.Pixel == nil {
		opts.Pixel = &PixelOptions{
			EnablePixel:  true,
			EnableBeacon: true,
			EnableAMP:    true,
		}
	}
	
	if opts.ConversionWindowHours <= 0 {
		opts.ConversionWindowHours = 24
	}
	
	return opts
}

// SetVariables 设置模板变量
func (t *TemplateLocale) SetVariables(variables map[string]interface{}) error {
	if variables == nil {
		t.Variables = nil
		return nil
	}
	
	data, err := json.Marshal(variables)
	if err != nil {
		return fmt.Errorf("marshal variables: %w", err)
	}
	
	t.Variables = datatypes.JSON(data)
	return nil
}

// GetVariables 获取模板变量
func (t *TemplateLocale) GetVariables() map[string]interface{} {
	if len(t.Variables) == 0 {
		return make(map[string]interface{})
	}
	
	var variables map[string]interface{}
	if err := json.Unmarshal(t.Variables, &variables); err != nil {
		return make(map[string]interface{})
	}
	
	return variables
}

// SetMetadata 设置元数据
func (t *TemplateLocale) SetMetadata(metadata map[string]interface{}) error {
	if metadata == nil {
		t.Metadata = nil
		return nil
	}
	
	data, err := json.Marshal(metadata)
	if err != nil {
		return fmt.Errorf("marshal metadata: %w", err)
	}
	
	t.Metadata = datatypes.JSON(data)
	return nil
}

// GetMetadata 获取元数据
func (t *TemplateLocale) GetMetadata() map[string]interface{} {
	if len(t.Metadata) == 0 {
		return make(map[string]interface{})
	}
	
	var metadata map[string]interface{}
	if err := json.Unmarshal(t.Metadata, &metadata); err != nil {
		return make(map[string]interface{})
	}
	
	return metadata
}

// IsTrackingEnabled 检查是否启用了追踪
func (t *TemplateLocale) IsTrackingEnabled() bool {
	opts := t.GetTrackingOptions()
	if opts == nil {
		return true // 默认启用
	}
	return opts.Enabled
}

// Validate 验证模板多语言数据
func (t *TemplateLocale) Validate() error {
	if t.TenantID <= 0 {
		return NewTemplateValidationError("tenant_id", "租户ID不能为空")
	}
	
	if t.TemplateID <= 0 {
		return NewTemplateValidationError("template_id", "模板ID不能为空")
	}
	
	if t.Locale == "" {
		return NewTemplateValidationError("locale", "语言代码不能为空")
	}
	
	if t.Name == "" {
		return NewTemplateValidationError("name", "模板名称不能为空")
	}
	
	if t.Subject == "" {
		return NewTemplateValidationError("subject", "邮件主题不能为空")
	}
	
	if !t.Status.IsValid() {
		return NewTemplateValidationError("status", "无效的模板状态")
	}
	
	// 验证追踪配置
	if opts := t.GetTrackingOptions(); opts != nil {
		if err := t.validateTrackingOptions(opts); err != nil {
			return err
		}
	}
	
	return nil
}

// validateTrackingOptions 验证追踪配置
func (t *TemplateLocale) validateTrackingOptions(opts *TrackingOptions) error {
	if opts.ConversionWindowHours < 0 || opts.ConversionWindowHours > 720 { // 最大30天
		return NewTemplateValidationError("conversion_window_hours", "转化窗口必须在0-720小时之间")
	}
	
	if opts.Attribution != nil {
		if opts.Attribution.WindowHours < 0 || opts.Attribution.WindowHours > 720 {
			return NewTemplateValidationError("attribution.window_hours", "归因窗口必须在0-720小时之间")
		}
	}
	
	return nil
}

// Clone 克隆模板多语言实体
func (t *TemplateLocale) Clone() *TemplateLocale {
	clone := &TemplateLocale{
		TenantID:     t.TenantID,
		TemplateID:   t.TemplateID,
		Locale:       t.Locale,
		Name:         t.Name,
		Subject:      t.Subject,
		PreHeader:    t.PreHeader,
		HtmlContent:  t.HtmlContent,
		TextContent:  t.TextContent,
		Status:       t.Status,
		Version:      t.Version + 1, // 新版本
		CreatedAt:    t.CreatedAt,
		UpdatedAt:    time.Now(),
	}
	
	// 深拷贝JSON字段
	if len(t.TrackingOptions) > 0 {
		clone.TrackingOptions = make(datatypes.JSON, len(t.TrackingOptions))
		copy(clone.TrackingOptions, t.TrackingOptions)
	}
	
	if len(t.Variables) > 0 {
		clone.Variables = make(datatypes.JSON, len(t.Variables))
		copy(clone.Variables, t.Variables)
	}
	
	if len(t.Metadata) > 0 {
		clone.Metadata = make(datatypes.JSON, len(t.Metadata))
		copy(clone.Metadata, t.Metadata)
	}
	
	return clone
}