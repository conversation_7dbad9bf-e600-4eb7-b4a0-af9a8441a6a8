package entity

import (
	"encoding/json"
	"time"
)

// AudienceSnapshot 受众快照实体
type AudienceSnapshot struct {
	SnapshotID     int64                  `json:"snapshot_id" gorm:"primaryKey;autoIncrement"`
	PlanID         int64                  `json:"plan_id" gorm:"not null;index:idx_audience_snapshot_plan"`
	TenantID       int64                  `json:"tenant_id" gorm:"not null;index:idx_audience_snapshot_tenant"`
	SegmentQuery   map[string]interface{} `json:"segment_query" gorm:"type:json;not null"`
	MaterializedAt time.Time              `json:"materialized_at" gorm:"not null"`
	TotalCount     int                    `json:"total_count" gorm:"not null"`
	CreatedAt      time.Time              `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (AudienceSnapshot) TableName() string {
	return "audience_snapshots"
}

// Validate 验证受众快照
func (as *AudienceSnapshot) Validate() error {
	if as.PlanID <= 0 {
		return NewAudienceSnapshotValidationError("plan_id", "计划ID不能为空")
	}

	if as.TenantID <= 0 {
		return NewAudienceSnapshotValidationError("tenant_id", "租户ID不能为空")
	}

	if as.SegmentQuery == nil || len(as.SegmentQuery) == 0 {
		return NewAudienceSnapshotValidationError("segment_query", "圈选条件不能为空")
	}

	if as.TotalCount < 0 {
		return NewAudienceSnapshotValidationError("total_count", "总数不能为负数")
	}

	return nil
}

// GetSegmentQueryJSON 获取圈选条件的JSON字符串
func (as *AudienceSnapshot) GetSegmentQueryJSON() (string, error) {
	if as.SegmentQuery == nil {
		return "{}", nil
	}

	data, err := json.Marshal(as.SegmentQuery)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// SetSegmentQueryFromJSON 从JSON字符串设置圈选条件
func (as *AudienceSnapshot) SetSegmentQueryFromJSON(jsonStr string) error {
	if jsonStr == "" {
		as.SegmentQuery = make(map[string]interface{})
		return nil
	}

	var query map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &query); err != nil {
		return err
	}

	as.SegmentQuery = query
	return nil
}

// IsEmpty 检查快照是否为空
func (as *AudienceSnapshot) IsEmpty() bool {
	return as.TotalCount == 0
}

// GetEstimatedBatchCount 获取预估批次数量
func (as *AudienceSnapshot) GetEstimatedBatchCount(batchSize int) int {
	if batchSize <= 0 {
		return 0
	}

	if as.TotalCount == 0 {
		return 0
	}

	return (as.TotalCount + batchSize - 1) / batchSize // 向上取整
}

// Clone 克隆受众快照
func (as *AudienceSnapshot) Clone() *AudienceSnapshot {
	clone := *as

	// 深拷贝圈选条件
	if as.SegmentQuery != nil {
		clone.SegmentQuery = make(map[string]interface{})
		for k, v := range as.SegmentQuery {
			clone.SegmentQuery[k] = v
		}
	}

	return &clone
}

// AudienceSnapshotSummary 受众快照摘要
type AudienceSnapshotSummary struct {
	SnapshotID     int64     `json:"snapshot_id"`
	PlanID         int64     `json:"plan_id"`
	TotalCount     int       `json:"total_count"`
	MaterializedAt time.Time `json:"materialized_at"`
	CreatedAt      time.Time `json:"created_at"`
}

// ToSummary 转换为摘要
func (as *AudienceSnapshot) ToSummary() *AudienceSnapshotSummary {
	return &AudienceSnapshotSummary{
		SnapshotID:     as.SnapshotID,
		PlanID:         as.PlanID,
		TotalCount:     as.TotalCount,
		MaterializedAt: as.MaterializedAt,
		CreatedAt:      as.CreatedAt,
	}
}
