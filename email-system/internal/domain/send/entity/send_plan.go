package entity

import (
	"encoding/json"
	"time"
)

// PlanType 发送计划类型
type PlanType string

const (
	PlanTypeCampaign   PlanType = "campaign"   // 活动群发
	PlanTypeAutomation PlanType = "automation" // 自动化旅程
	PlanTypeManual     PlanType = "manual"     // 手动任务
)

// PlanStatus 发送计划状态
type PlanStatus string

const (
	PlanStatusDraft     PlanStatus = "draft"     // 草稿
	PlanStatusRunning   PlanStatus = "running"   // 运行中
	PlanStatusPaused    PlanStatus = "paused"    // 暂停
	PlanStatusCompleted PlanStatus = "completed" // 已完成
	PlanStatusCanceled  PlanStatus = "canceled"  // 已取消
	PlanStatusExpired   PlanStatus = "expired"   // 已过期
)

// RetryPolicy 重试策略
type RetryPolicy struct {
	Mode             string   `json:"mode"`               // NONE|RETRY_LINEAR|RETRY_EXPONENTIAL
	MaxAttempts      int      `json:"max_attempts"`       // 最大重试次数
	InitialBackoffMs int      `json:"initial_backoff_ms"` // 初始退避时间(毫秒)
	MaxBackoffMs     int      `json:"max_backoff_ms"`     // 最大退避时间(毫秒)
	RetryOn          []string `json:"retry_on"`           // 重试的错误类型
}

// SendPlan 发送计划实体
type SendPlan struct {
	PlanID       int64        `json:"plan_id" gorm:"primaryKey;autoIncrement"`
	TenantID     int64        `json:"tenant_id" gorm:"not null;index:idx_send_plan_tenant"`
	PlanType     PlanType     `json:"plan_type" gorm:"not null"`
	DisplayName  string       `json:"display_name" gorm:"not null;size:255"`
	TemplateID   string       `json:"template_id" gorm:"not null;size:128"`
	Priority     int          `json:"priority" gorm:"not null;default:5"`
	ScheduleFrom *time.Time   `json:"schedule_from"`
	ScheduleTo   *time.Time   `json:"schedule_to"`
	Deadline     *time.Time   `json:"deadline"`
	Status       PlanStatus   `json:"status" gorm:"not null;default:'draft'"`
	RetryPolicy  *RetryPolicy `json:"retry_policy" gorm:"type:json"`
	CreatedAt    time.Time    `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time    `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (SendPlan) TableName() string {
	return "send_plans"
}

// IsValid 检查计划类型是否有效
func (pt PlanType) IsValid() bool {
	switch pt {
	case PlanTypeCampaign, PlanTypeAutomation, PlanTypeManual:
		return true
	default:
		return false
	}
}

// IsValid 检查计划状态是否有效
func (ps PlanStatus) IsValid() bool {
	switch ps {
	case PlanStatusDraft, PlanStatusRunning, PlanStatusPaused, PlanStatusCompleted, PlanStatusCanceled, PlanStatusExpired:
		return true
	default:
		return false
	}
}

// GetDefaultPriority 获取默认优先级
func (pt PlanType) GetDefaultPriority() int {
	switch pt {
	case PlanTypeAutomation:
		return 8
	case PlanTypeCampaign:
		return 5
	case PlanTypeManual:
		return 4
	default:
		return 5
	}
}

// Validate 验证发送计划
func (sp *SendPlan) Validate() error {
	if sp.TenantID <= 0 {
		return NewSendPlanValidationError("tenant_id", "租户ID不能为空")
	}

	if !sp.PlanType.IsValid() {
		return NewSendPlanValidationError("plan_type", "无效的计划类型")
	}

	if sp.DisplayName == "" {
		return NewSendPlanValidationError("display_name", "显示名称不能为空")
	}

	if len(sp.DisplayName) > 255 {
		return NewSendPlanValidationError("display_name", "显示名称长度不能超过255个字符")
	}

	if sp.TemplateID == "" {
		return NewSendPlanValidationError("template_id", "模板ID不能为空")
	}

	if len(sp.TemplateID) > 128 {
		return NewSendPlanValidationError("template_id", "模板ID长度不能超过128个字符")
	}

	if sp.Priority < 1 || sp.Priority > 10 {
		return NewSendPlanValidationError("priority", "优先级必须在1-10之间")
	}

	if !sp.Status.IsValid() {
		return NewSendPlanValidationError("status", "无效的计划状态")
	}

	// 验证时间窗口
	if sp.ScheduleFrom != nil && sp.ScheduleTo != nil {
		if sp.ScheduleTo.Before(*sp.ScheduleFrom) {
			return NewSendPlanValidationError("schedule_to", "结束时间不能早于开始时间")
		}
	}

	// 验证截止时间
	if sp.Deadline != nil {
		if sp.ScheduleFrom != nil && sp.Deadline.Before(*sp.ScheduleFrom) {
			return NewSendPlanValidationError("deadline", "截止时间不能早于开始时间")
		}
	}

	// 验证重试策略
	if sp.RetryPolicy != nil {
		if err := sp.RetryPolicy.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证重试策略
func (rp *RetryPolicy) Validate() error {
	validModes := []string{"NONE", "RETRY_LINEAR", "RETRY_EXPONENTIAL"}
	validMode := false
	for _, mode := range validModes {
		if rp.Mode == mode {
			validMode = true
			break
		}
	}
	if !validMode {
		return NewSendPlanValidationError("retry_policy.mode", "无效的重试模式")
	}

	if rp.MaxAttempts < 1 || rp.MaxAttempts > 10 {
		return NewSendPlanValidationError("retry_policy.max_attempts", "最大重试次数必须在1-10之间")
	}

	if rp.InitialBackoffMs < 0 {
		return NewSendPlanValidationError("retry_policy.initial_backoff_ms", "初始退避时间不能为负数")
	}

	if rp.MaxBackoffMs < rp.InitialBackoffMs {
		return NewSendPlanValidationError("retry_policy.max_backoff_ms", "最大退避时间不能小于初始退避时间")
	}

	return nil
}

// CanStart 检查是否可以开始执行
func (sp *SendPlan) CanStart() bool {
	if sp.Status != PlanStatusDraft && sp.Status != PlanStatusPaused {
		return false
	}

	now := time.Now()

	// 检查是否在计划时间窗口内
	if sp.ScheduleFrom != nil && now.Before(*sp.ScheduleFrom) {
		return false
	}

	// 检查是否已过截止时间
	if sp.Deadline != nil && now.After(*sp.Deadline) {
		return false
	}

	return true
}

// CanPause 检查是否可以暂停
func (sp *SendPlan) CanPause() bool {
	return sp.Status == PlanStatusRunning
}

// CanCancel 检查是否可以取消
func (sp *SendPlan) CanCancel() bool {
	return sp.Status == PlanStatusDraft || sp.Status == PlanStatusRunning || sp.Status == PlanStatusPaused
}

// IsExpired 检查是否已过期
func (sp *SendPlan) IsExpired() bool {
	if sp.Deadline == nil {
		return false
	}
	return time.Now().After(*sp.Deadline)
}

// Start 开始执行计划
func (sp *SendPlan) Start() error {
	if !sp.CanStart() {
		return NewSendPlanStateError("计划当前状态不允许开始执行")
	}

	sp.Status = PlanStatusRunning
	sp.UpdatedAt = time.Now()
	return nil
}

// Pause 暂停计划
func (sp *SendPlan) Pause() error {
	if !sp.CanPause() {
		return NewSendPlanStateError("计划当前状态不允许暂停")
	}

	sp.Status = PlanStatusPaused
	sp.UpdatedAt = time.Now()
	return nil
}

// Cancel 取消计划
func (sp *SendPlan) Cancel() error {
	if !sp.CanCancel() {
		return NewSendPlanStateError("计划当前状态不允许取消")
	}

	sp.Status = PlanStatusCanceled
	sp.UpdatedAt = time.Now()
	return nil
}

// Complete 完成计划
func (sp *SendPlan) Complete() error {
	if sp.Status != PlanStatusRunning {
		return NewSendPlanStateError("只有运行中的计划可以标记为完成")
	}

	sp.Status = PlanStatusCompleted
	sp.UpdatedAt = time.Now()
	return nil
}

// MarkExpired 标记为过期
func (sp *SendPlan) MarkExpired() {
	sp.Status = PlanStatusExpired
	sp.UpdatedAt = time.Now()
}

// GetRetryPolicyJSON 获取重试策略的JSON字符串
func (sp *SendPlan) GetRetryPolicyJSON() (string, error) {
	if sp.RetryPolicy == nil {
		return "", nil
	}

	data, err := json.Marshal(sp.RetryPolicy)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// SetRetryPolicyFromJSON 从JSON字符串设置重试策略
func (sp *SendPlan) SetRetryPolicyFromJSON(jsonStr string) error {
	if jsonStr == "" {
		sp.RetryPolicy = nil
		return nil
	}

	var policy RetryPolicy
	if err := json.Unmarshal([]byte(jsonStr), &policy); err != nil {
		return err
	}

	if err := policy.Validate(); err != nil {
		return err
	}

	sp.RetryPolicy = &policy
	return nil
}

// Clone 克隆发送计划
func (sp *SendPlan) Clone() *SendPlan {
	clone := *sp

	if sp.ScheduleFrom != nil {
		scheduleFrom := *sp.ScheduleFrom
		clone.ScheduleFrom = &scheduleFrom
	}

	if sp.ScheduleTo != nil {
		scheduleTo := *sp.ScheduleTo
		clone.ScheduleTo = &scheduleTo
	}

	if sp.Deadline != nil {
		deadline := *sp.Deadline
		clone.Deadline = &deadline
	}

	if sp.RetryPolicy != nil {
		retryPolicy := *sp.RetryPolicy
		clone.RetryPolicy = &retryPolicy
	}

	return &clone
}
