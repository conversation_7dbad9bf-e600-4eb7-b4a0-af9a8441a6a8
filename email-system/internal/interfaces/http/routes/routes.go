package routes

import (
	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/email-system/pkg/config"
	httpmiddleware "gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(engine *gin.Engine, container *container.DependencyContainer, appConfig *config.AppConfig, logger logiface.Logger) {
	// 设置中间件
	setupMiddlewares(engine, appConfig, logger)

	// 设置API路由
	setupAPIRoutes(engine, container)

	// 设置追踪路由
	setupTrackingRoutes(engine, container)

	// 设置退订和同意路由
	setupUnsubscribeRoutes(engine, container, appConfig)

	// 设置规则路由
	setupRuleRoutes(engine, container)

	// 设置发送路由
	setupSendRoutes(engine, container)

	// 设置健康检查路由
	setupHealthRoutes(engine)
}

// setupMiddlewares 设置中间件
func setupMiddlewares(engine *gin.Engine, appConfig *config.AppConfig, logger logiface.Logger) {
	// 恢复中间件
	engine.Use(gin.Recovery())

	// 请求ID中间件
	engine.Use(httpmiddleware.RequestID())

	// 日志中间件
	// 可选：接入访问日志中间件
	// engine.Use(httpmiddleware.AccessLogMiddleware())

	// 用户上下文中间件
	// 统一用户与应用信息中间件（如需）可用 SetupCommonMiddleware 配置

	// CORS中间件
	// CORS 可按需接入

	// 限流中间件（如果需要）
	// engine.Use(httpmiddleware.RateLimitMiddleware())
}

// setupAPIRoutes 设置API路由
func setupAPIRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	// API v1 路由组
	v1 := engine.Group("/api/v1")
	{
		// 联系人路由
		setupContactRoutes(v1, container)

		// 标签路由
		setupTagRoutes(v1, container)

		// 人群圈选路由
		setupSegmentRoutes(v1, container)

		// 分析路由
		setupAnalyticsRoutes(v1, container)
	}
}

// setupContactRoutes 设置联系人路由
func setupContactRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	contactHandler := container.GetContactHandler()
	contacts := rg.Group("/contacts")
	{
		// 基础CRUD操作
		contacts.POST("", contactHandler.Create)             // 创建联系人
		contacts.PUT("", contactHandler.Update)              // 更新联系人
		contacts.GET("", contactHandler.Get)                 // 获取联系人（通过查询参数）
		contacts.POST("/search", contactHandler.Search)      // 搜索联系人
		contacts.DELETE("", contactHandler.Delete)           // 删除联系人
		contacts.POST("/batch", contactHandler.BatchCreate)  // 批量创建联系人
		contacts.PUT("/batch", contactHandler.BatchUpdate)   // 批量更新联系人
		contacts.PUT("/status", contactHandler.UpdateStatus) // 批量更新状态
	}
}

// setupTagRoutes 设置标签路由
func setupTagRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	tagHandler := container.GetTagHandler()
	tags := rg.Group("/tags")
	{
		// 基础CRUD操作
		tags.POST("", tagHandler.Create)          // 创建标签
		tags.PUT("", tagHandler.Update)           // 更新标签
		tags.GET("", tagHandler.Get)              // 获取标签（通过查询参数）
		tags.POST("/list", tagHandler.List)       // 获取标签列表
		tags.DELETE("", tagHandler.Delete)        // 删除标签
		tags.POST("/assign", tagHandler.Assign)   // 分配标签
		tags.POST("/refresh", tagHandler.Refresh) // 刷新标签
		tags.POST("/merge", tagHandler.Merge)     // 合并标签
		tags.PUT("/rename", tagHandler.Rename)    // 重命名标签
		tags.GET("/popular", tagHandler.Popular)  // 获取热门标签
		tags.GET("/recent", tagHandler.Recent)    // 获取最近使用的标签
	}
}

// setupSegmentRoutes 设置人群圈选路由
func setupSegmentRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	segmentHandler := container.GetSegmentHandler()
	segments := rg.Group("/segments")
	{
		// 基础CRUD操作
		segments.POST("", segmentHandler.Create)                       // 创建人群圈选
		segments.PUT("", segmentHandler.Update)                        // 更新人群圈选
		segments.GET("/:id", segmentHandler.Get)                       // 获取人群圈选
		segments.POST("/list", segmentHandler.List)                    // 获取人群圈选列表
		segments.DELETE("/:id", segmentHandler.Delete)                 // 删除人群圈选
		segments.POST("/preview", segmentHandler.Preview)              // 预览人群圈选
		segments.POST("/:id/rebuild", segmentHandler.Rebuild)          // 重建人群圈选
		segments.GET("/:id/export", segmentHandler.Export)             // 导出人群圈选
		segments.GET("/jobs/:jobId", segmentHandler.GetJob)            // 获取任务状态
		segments.POST("/jobs/:jobId/cancel", segmentHandler.CancelJob) // 取消任务
	}
}

// setupHealthRoutes 设置健康检查路由
func setupHealthRoutes(engine *gin.Engine) {
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "email-system",
		})
	})

	engine.GET("/ready", func(c *gin.Context) {
		// TODO: 检查依赖服务状态
		c.JSON(200, gin.H{
			"status": "ready",
		})
	})
}

// setupUnsubscribeRoutes 设置退订和同意路由
func setupUnsubscribeRoutes(engine *gin.Engine, container *container.DependencyContainer, appConfig *config.AppConfig) {
	// 直接使用容器中的退订处理器
	unsubscribeHandler := container.GetUnsubscribeHandler()

	// 公开路由（不需要认证）
	public := engine.Group("/")
	{
		// 退订页面和处理
		public.GET("/unsubscribe", unsubscribeHandler.ShowUnsubscribePage)
		public.POST("/unsubscribe", unsubscribeHandler.ProcessUnsubscribe)

		// 同意页面和处理
		public.GET("/consent", unsubscribeHandler.ShowConsentPage)
		public.POST("/consent", unsubscribeHandler.ProcessConsentForm)
	}

	// API路由
	api := engine.Group("/api/v1")
	{
		// 退订和同意API
		api.POST("/unsubscribe", unsubscribeHandler.ProcessUnsubscribe)
		api.POST("/consent/grant", unsubscribeHandler.GrantConsent)
		api.POST("/consent/withdraw", unsubscribeHandler.WithdrawConsent)
		api.GET("/consent/status", unsubscribeHandler.GetConsentStatus)
		api.POST("/unsubscribe/generate-link", unsubscribeHandler.GenerateUnsubscribeLink)
	}
}

// setupRuleRoutes 设置规则路由
func setupRuleRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	ruleHandler := container.GetRuleHandler()

	// API路由
	api := engine.Group("/api/v1")
	{
		// 规则相关路由
		ruleGroup := api.Group("/rules")
		{
			ruleGroup.POST("/validate", ruleHandler.ValidateRule)
			ruleGroup.POST("/evaluate", ruleHandler.EvaluateRule)
			ruleGroup.GET("/templates", ruleHandler.GetRuleTemplates)
			ruleGroup.POST("/parse", ruleHandler.ParseRule)
			ruleGroup.POST("/build-contact", ruleHandler.BuildContactRule)
			ruleGroup.GET("/operators", ruleHandler.GetRuleOperators)
			ruleGroup.GET("/field-types", ruleHandler.GetFieldTypes)
		}

		// 标签规则相关路由
		tagGroup := api.Group("/tags")
		{
			tagGroup.POST("/:id/refresh", container.GetTagHandler().RefreshTag)
			tagGroup.POST("/preview-rule", container.GetTagHandler().PreviewTagRule)
			tagGroup.POST("/validate-rule", container.GetTagHandler().ValidateTagRule)
			tagGroup.GET("/rule-fields", container.GetTagHandler().GetTagRuleFields)
		}

		// 人群圈选规则相关路由
		segmentGroup := api.Group("/segments")
		{
			segmentGroup.POST("/preview-rule", container.GetSegmentHandler().PreviewSegmentRule)
			segmentGroup.POST("/validate-rule", container.GetSegmentHandler().ValidateSegmentRule)
			segmentGroup.POST("/:id/rebuild", container.GetSegmentHandler().RebuildSegment)
		}
	}
}

// setupSendRoutes 设置发送路由
func setupSendRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	sendHandler := container.GetSendHandler()

	// API路由
	api := engine.Group("/api/v1")
	{
		// 发送计划路由
		sendPlanGroup := api.Group("/send-plans")
		{
			sendPlanGroup.POST("", sendHandler.CreateSendPlan)
			sendPlanGroup.GET("", sendHandler.ListSendPlans)
			sendPlanGroup.GET("/:id", sendHandler.GetSendPlan)
			sendPlanGroup.PUT("/:id", sendHandler.UpdateSendPlan)
			sendPlanGroup.DELETE("/:id", sendHandler.DeleteSendPlan)
			sendPlanGroup.POST("/:id/start", sendHandler.StartSendPlan)
			sendPlanGroup.POST("/:id/pause", sendHandler.PauseSendPlan)
			sendPlanGroup.POST("/:id/cancel", sendHandler.CancelSendPlan)
		}
	}
}

// setupTrackingRoutes 设置追踪路由
func setupTrackingRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	trackingHandler := container.GetTrackingHandler()

	// 追踪路由（公开访问，无需认证）
	tracking := engine.Group("/api/tracking")
	{
		// 像素追踪
		tracking.GET("/open", trackingHandler.HandleOpen)
		tracking.GET("/beacon", trackingHandler.HandleBeacon)
		tracking.GET("/amp/open", trackingHandler.HandleAMPOpen)

		// 点击追踪
		tracking.GET("/redirect", trackingHandler.HandleClick)

		// 转化追踪
		tracking.POST("/conversion", trackingHandler.HandleConversion)

		// 入站回复追踪
		tracking.POST("/inbound", trackingHandler.HandleInbound)

		// 批量事件处理（用于边缘节点）
		tracking.POST("/batch", trackingHandler.HandleBatchEvents)
	}
}

// setupAnalyticsRoutes 设置分析路由
func setupAnalyticsRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	analyticsHandler := container.GetAnalyticsHandler()

	// 分析路由
	analytics := rg.Group("/analytics")
	{
		// 活动统计
		analytics.GET("/campaigns/:campaignId/stats", analyticsHandler.GetCampaignStats)
		analytics.GET("/campaigns/:campaignId/realtime", analyticsHandler.GetRealtimeStats)

		// 报表生成
		analytics.POST("/reports/generate", analyticsHandler.GenerateReport)
		analytics.GET("/reports/:reportId", analyticsHandler.GetReport)
		analytics.GET("/reports/:reportId/download", analyticsHandler.DownloadReport)

		// 数据导出
		analytics.POST("/exports", analyticsHandler.CreateExport)
		analytics.GET("/exports/:exportId", analyticsHandler.GetExportStatus)
		analytics.GET("/exports/:exportId/download", analyticsHandler.DownloadExport)

		// 链接分析
		analytics.GET("/campaigns/:campaignId/links", analyticsHandler.GetLinkAnalytics)
		analytics.GET("/campaigns/:campaignId/heatmap", analyticsHandler.GetHeatmap)

		// 用户旅程
		analytics.GET("/subscribers/:subscriberId/journey", analyticsHandler.GetUserJourney)
		analytics.GET("/campaigns/:campaignId/funnel", analyticsHandler.GetConversionFunnel)

		// 实时指标
		analytics.GET("/realtime/overview", analyticsHandler.GetRealtimeOverview)
		analytics.GET("/realtime/events", analyticsHandler.GetRealtimeEvents)
	}
}
