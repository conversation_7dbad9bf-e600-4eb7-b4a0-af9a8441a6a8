package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/email-system/internal/interfaces/http/routes"
	"gitee.com/heiyee/platforms/email-system/pkg/config"

	// "gitee.com/heiyee/platforms/pkg/grpcregistry"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/otel"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
)

func main() {
	// 解析命令行参数
	var configPath = flag.String("config", "configs/app.yaml", "配置文件路径")
	flag.Parse()

	// 初始化配置
	appConfig, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logiface.GetLogger()

	// 初始化 OpenTelemetry
	if appConfig.Otel.Endpoint != "" {
		cleanup, err := otel.InitTracerProvider(appConfig.Otel.ServiceName, appConfig.Otel.Endpoint)
		if err != nil {
			logger.Error(context.Background(), "Failed to initialize OpenTelemetry", logiface.Error(err))
		} else {
			defer func() { _ = cleanup(context.Background()) }()
			logger.Info(context.Background(), "OpenTelemetry initialized successfully")
		}
	}

	// 初始化用户上下文
	// usercontext global initialize not required

	// 初始化ID生成器
	// ID generator not required for minimal build path

	// 创建依赖注入容器
	dependencyContainer := container.NewDependencyContainer(appConfig, logger)

	// 初始化依赖
	if err := dependencyContainer.Initialize(); err != nil {
		logger.Error(context.Background(), "Failed to initialize dependencies", logiface.Error(err))
		return
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动HTTP服务器
	httpServer := startHTTPServer(ctx, appConfig, dependencyContainer, logger)

	// 启动gRPC服务器（如果配置了）
	var grpcServer *grpc.Server
	if appConfig.GRPC.Port > 0 {
		grpcServer = startGRPCServer(ctx, appConfig, dependencyContainer, logger)
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info(ctx, "Shutting down servers...")

	// 优雅关闭HTTP服务器
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		logger.Error(ctx, "HTTP server forced to shutdown", logiface.Error(err))
	}

	// 优雅关闭gRPC服务器
	if grpcServer != nil {
		grpcServer.GracefulStop()
	}

	logger.Info(ctx, "Servers shutdown complete")
}

func startHTTPServer(ctx context.Context, appConfig *config.AppConfig, container *container.DependencyContainer, logger logiface.Logger) *http.Server {
	// 设置Gin模式
	if appConfig.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	engine := gin.New()

	// 设置路由
	routes.SetupRoutes(engine, container, appConfig, logger)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", appConfig.Server.Port),
		Handler: engine,
	}

	// 启动服务器
	go func() {
		logger.Info(ctx, "Starting HTTP server", logiface.Int("port", appConfig.Server.Port))
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Error(ctx, "Failed to start HTTP server", logiface.Error(err))
		}
	}()

	return server
}

func startGRPCServer(ctx context.Context, appConfig *config.AppConfig, container *container.DependencyContainer, logger logiface.Logger) *grpc.Server {
	// 创建gRPC服务器
	server := grpc.NewServer()

	// 注册服务
	// TODO: 注册gRPC服务

	// 启动服务器
	go func() {
		lis, err := net.Listen("tcp", fmt.Sprintf(":%d", appConfig.GRPC.Port))
		if err != nil {
			logger.Error(ctx, "Failed to listen on gRPC port", logiface.Error(err))
		}

		logger.Info(ctx, "Starting gRPC server", logiface.Int("port", appConfig.GRPC.Port))
		if err := server.Serve(lis); err != nil {
			logger.Error(ctx, "Failed to start gRPC server", logiface.Error(err))
		}
	}()

	// 注册到服务发现（如果配置了）
	// Registry integration optional in minimal build path

	return server
}
