package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// AppConfig 应用配置
type AppConfig struct {
	Server      ServerConfig      `yaml:"server"`
	Database    DatabaseConfig    `yaml:"database"`
	Redis       RedisConfig       `yaml:"redis"`
	Log         LogConfig         `yaml:"log"`
	Otel        OtelConfig        `yaml:"otel"`
	GRPC        GRPCConfig        `yaml:"grpc"`
	Registry    RegistryConfig    `yaml:"registry"`
	IDGenerator IDGeneratorConfig `yaml:"id_generator"`
	Email       EmailConfig       `yaml:"email"`
	Storage     StorageConfig     `yaml:"storage"`
}

// ServerConfig HTTP服务器配置
type ServerConfig struct {
	Port    int    `yaml:"port"`
	Mode    string `yaml:"mode"`
	BaseURL string `yaml:"base_url"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `yaml:"driver"`
	DSN             string `yaml:"dsn"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"`
	ConnMaxIdleTime int    `yaml:"conn_max_idle_time"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	TimeFormat string `yaml:"time_format"`
}

// OtelConfig OpenTelemetry配置
type OtelConfig struct {
	Endpoint    string `yaml:"endpoint"`
	ServiceName string `yaml:"service_name"`
}

// GRPCConfig gRPC服务器配置
type GRPCConfig struct {
	Port int `yaml:"port"`
}

// RegistryConfig 服务注册配置
type RegistryConfig struct {
	Enabled bool   `yaml:"enabled"`
	Type    string `yaml:"type"`
	Address string `yaml:"address"`
}

// IDGeneratorConfig ID生成器配置
type IDGeneratorConfig struct {
	WorkerID     int64 `yaml:"worker_id"`
	DatacenterID int64 `yaml:"datacenter_id"`
}

// EmailConfig 邮件模块配置
type EmailConfig struct {
	ModuleEndpoint string `yaml:"module_endpoint"`
	InternalAppID  int64  `yaml:"internal_app_id"`
	Timeout        int    `yaml:"timeout"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type      string            `yaml:"type"`
	LocalPath string            `yaml:"local_path"`
	S3        S3Config          `yaml:"s3"`
	Options   map[string]string `yaml:"options"`
}

// S3Config S3存储配置
type S3Config struct {
	Region    string `yaml:"region"`
	Bucket    string `yaml:"bucket"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	Endpoint  string `yaml:"endpoint"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*AppConfig, error) {
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config file not found: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析YAML
	var config AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	// 环境变量覆盖
	overrideWithEnv(&config)

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults(config *AppConfig) {
	if config.Server.Port == 0 {
		config.Server.Port = 8085
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
	if config.Log.Format == "" {
		config.Log.Format = "json"
	}
	if config.Log.TimeFormat == "" {
		config.Log.TimeFormat = "2006-01-02T15:04:05.000Z07:00"
	}
	if config.Otel.ServiceName == "" {
		config.Otel.ServiceName = "email-system"
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = 100
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = 10
	}
	if config.Database.ConnMaxLifetime == 0 {
		config.Database.ConnMaxLifetime = 3600 // 1小时
	}
	if config.Database.ConnMaxIdleTime == 0 {
		config.Database.ConnMaxIdleTime = 600 // 10分钟
	}
	if config.Email.Timeout == 0 {
		config.Email.Timeout = 30 // 30秒
	}
	if config.Storage.Type == "" {
		config.Storage.Type = "local"
	}
	if config.Storage.LocalPath == "" {
		config.Storage.LocalPath = "./uploads"
	}
}

// overrideWithEnv 使用环境变量覆盖配置
func overrideWithEnv(config *AppConfig) {
	if port := os.Getenv("EMAIL_SYSTEM_PORT"); port != "" {
		// 可以添加环境变量覆盖逻辑
	}
	if dsn := os.Getenv("EMAIL_SYSTEM_DATABASE_DSN"); dsn != "" {
		config.Database.DSN = dsn
	}
	if redisAddr := os.Getenv("EMAIL_SYSTEM_REDIS_ADDR"); redisAddr != "" {
		config.Redis.Addr = redisAddr
	}
}

// GetConfigDir 获取配置文件目录
func GetConfigDir() string {
	if dir := os.Getenv("EMAIL_SYSTEM_CONFIG_DIR"); dir != "" {
		return dir
	}
	return "configs"
}

// GetDefaultConfigPath 获取默认配置文件路径
func GetDefaultConfigPath() string {
	return filepath.Join(GetConfigDir(), "app.yaml")
}
