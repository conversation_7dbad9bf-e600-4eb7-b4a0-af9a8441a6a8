package entity

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// IDGenerator ID生成器接口 - 为依赖注入设计
type IDGenerator interface {
	// 业务类型特定的ID生成方法
	GenerateUserID(ctx context.Context) (int64, error)
	GenerateTenantID(ctx context.Context) (int64, error)
	GenerateRoleID(ctx context.Context) (int64, error)
	GeneratePermissionID(ctx context.Context) (int64, error)
	GenerateDepartmentID(ctx context.Context) (int64, error)
	GeneratePositionID(ctx context.Context) (int64, error)
	GenerateResourceID(ctx context.Context) (int64, error)

	// 通用ID生成方法
	GenerateID(ctx context.Context, businessType string) (int64, error)

	// 临时ID生成方法（使用雪花算法）
	GenerateSessionID(ctx context.Context) (string, error)
	GenerateRequestID(ctx context.Context) (string, error)
}

// EntityFactory 实体工厂 - 使用依赖注入的ID生成器，只负责生成ID
type EntityFactory struct {
	idGenerator IDGenerator
}

// NewEntityFactory 创建实体工厂
func NewEntityFactory(idGenerator IDGenerator) *EntityFactory {
	return &EntityFactory{
		idGenerator: idGenerator,
	}
}

// NewUser 创建新用户 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewUser(ctx context.Context, tenantID int64, username, email, realName string, password value_object.Password) (*User, error) {
	userID, err := f.idGenerator.GenerateUserID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %w", err)
	}

	return &User{
		ID:       userID,
		TenantID: tenantID,
		Username: username,
		Email:    email,
		RealName: realName,
		Password: password,
		Status:   value_object.UserStatusActive,
	}, nil
}

// NewTenant 创建新租户 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewTenant(ctx context.Context, tenantCode, tenantName string) (*Tenant, error) {
	tenantID, err := f.idGenerator.GenerateTenantID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tenant ID: %w", err)
	}

	return &Tenant{
		ID:               tenantID,
		TenantCode:       tenantCode,
		TenantName:       tenantName,
		Status:           "active",
		MaxUsers:         1000,
		MaxStorage:       10737418240, // 10GB
		SubscriptionPlan: "basic",
	}, nil
}

// NewRole 创建新角色 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewRole(ctx context.Context, tenantID int64, name, code, displayName, description string) (*Role, error) {
	roleID, err := f.idGenerator.GenerateRoleID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate role ID: %w", err)
	}

	return &Role{
		ID:          roleID,
		TenantID:    tenantID,
		Name:        name,
		Code:        code,
		DisplayName: displayName,
		Description: description,
		Status:      "active",
		IsSystem:    false,
	}, nil
}

// NewPermission 创建新权限 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewPermission(ctx context.Context, tenantID int64, name, code, displayName, description string, resourceID int64, scope string) (*Permission, error) {
	permissionID, err := f.idGenerator.GeneratePermissionID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate permission ID: %w", err)
	}

	return &Permission{
		ID:          permissionID,
		TenantID:    tenantID,
		Name:        name,
		Code:        code,
		DisplayName: displayName,
		Description: description,
		ResourceID:  resourceID,
		Scope:       scope,
		Status:      "active",
		IsSystem:    false,
	}, nil
}

// NewDepartment 创建新部门 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewDepartment(ctx context.Context, tenantID int64, name, code, description string) (*Department, error) {
	departmentID, err := f.idGenerator.GenerateDepartmentID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate department ID: %w", err)
	}

	return &Department{
		ID:          departmentID,
		TenantID:    tenantID,
		Name:        name,
		Code:        code,
		Description: description,
		Level:       1,
		Sort:        0,
		Status:      "active",
	}, nil
}

// NewPosition 创建新职位 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewPosition(ctx context.Context, tenantID int64, name, code, description string) (*Position, error) {
	positionID, err := f.idGenerator.GeneratePositionID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate position ID: %w", err)
	}

	return &Position{
		ID:          positionID,
		TenantID:    tenantID,
		Name:        name,
		Code:        code,
		Description: description,
		Level:       1,
		Sort:        0,
		Status:      "active",
		IsSystem:    false,
	}, nil
}
