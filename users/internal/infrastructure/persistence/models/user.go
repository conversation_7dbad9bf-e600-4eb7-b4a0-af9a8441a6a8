package models

import (
	"database/sql/driver"
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// Bool 自定义bool类型，用于处理MySQL BIT(1)类型
type Bool bool

// Scan 实现sql.Scanner接口
func (b *Bool) Scan(value interface{}) error {
	if value == nil {
		*b = false
		return nil
	}

	switch v := value.(type) {
	case []byte:
		// 处理BIT(1)类型
		if len(v) == 1 {
			*b = Bool(v[0] == 1)
		} else {
			*b = false
		}
	case bool:
		*b = Bool(v)
	case int64:
		*b = Bool(v != 0)
	case int:
		*b = Bool(v != 0)
	default:
		*b = false
	}

	return nil
}

// Value 实现driver.Valuer接口
func (b Bool) Value() (driver.Value, error) {
	if b {
		return int64(1), nil
	}
	return int64(0), nil
}

// ToBool 转换为bool类型
func (b Bool) ToBool() bool {
	return bool(b)
}

// UserModel 用户数据模型 - 仅用于数据库操作
type UserModel struct {
	ID                int64      `gorm:"primaryKey"`
	TenantID          int64      `gorm:"not null;index"`
	InternalAppID     int64      `gorm:"not null;index"` // 应用ID，bigint类型提升性能
	Username          string     `gorm:"uniqueIndex;not null;size:50"`
	Email             string     `gorm:"not null;size:100`
	Phone             string     `gorm:"size:20"`
	RealName          string     `gorm:"size:100"`
	Avatar            string     `gorm:"size:255"`
	Status            string     `gorm:"type:varchar(20);not null;default:'active'"`
	IsSystem          Bool       `gorm:"type:tinyint(1);not null;default:0"` // 是否系统用户
	Password          string     `gorm:"not null"`                           // 修正：数据库字段名是password，不是password_hash
	PasswordChangedAt *time.Time `gorm:"column:password_changed_at"`         // 新增：密码最后修改时间
	LastLoginAt       *time.Time `gorm:"column:last_login_at"`
	LastLoginIP       string     `gorm:"size:45;column:last_login_ip"`
	LoginCount        int        `gorm:"default:0;column:login_count"`
	FailedCount       int        `gorm:"default:0;column:failed_count"` // 修正：数据库字段名是failed_count，不是failed_login_count
	LockedAt          *time.Time `gorm:"column:locked_at"`
	LockedUntil       *time.Time `gorm:"column:locked_until"` // 修正：数据库字段名是locked_until，不是lock_until
	LockReason        string     `gorm:"size:200;column:lock_reason"`
	CreatedAt         time.Time  `gorm:"autoCreateTime"`
	UpdatedAt         time.Time  `gorm:"autoUpdateTime"`
	DeletedAt         *time.Time `gorm:"index"`

	// 组织架构相关
	DepartmentID *int64     `gorm:"index"`   // 所属部门ID
	PositionID   *int64     `gorm:"index"`   // 职位ID
	EmployeeID   string     `gorm:"size:50"` // 员工编号
	HireDate     *time.Time // 入职日期

}

// TableName 指定表名
func (UserModel) TableName() string {
	return "users"
}

// GetTenantID 获取租户ID
func (u *UserModel) GetTenantID() int64 {
	return u.TenantID
}

// SetTenantID 设置租户ID
func (u *UserModel) SetTenantID(tenantID int64) {
	u.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (u *UserModel) GetInternalAppID() int64 {
	return u.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (u *UserModel) SetInternalAppID(appID int64) {
	u.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (u *UserModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (u *UserModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (u *UserModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (u *UserModel) ToDomainEntity() *entity.User {
	user := &entity.User{
		ID:                u.ID,
		TenantID:          u.TenantID,
		InternalAppID:     u.InternalAppID,
		Username:          u.Username,
		Email:             u.Email,
		Phone:             u.Phone,
		RealName:          u.RealName,
		Avatar:            u.Avatar,
		Status:            value_object.UserStatus(u.Status),
		IsSystem:          entity.Bool(u.IsSystem.ToBool()),
		Password:          *value_object.NewPasswordFromHash(u.Password),
		PasswordChangedAt: u.PasswordChangedAt, // 新增字段
		LastLoginAt:       u.LastLoginAt,
		LastLoginIP:       u.LastLoginIP,
		LoginCount:        u.LoginCount,
		FailedCount:       u.FailedCount,
		LockedAt:          u.LockedAt,
		LockedUntil:       u.LockedUntil,
		LockReason:        u.LockReason,
		CreatedAt:         u.CreatedAt,
		UpdatedAt:         u.UpdatedAt,
		DeletedAt:         u.DeletedAt,
		DepartmentID:      u.DepartmentID,
		PositionID:        u.PositionID,
		EmployeeID:        u.EmployeeID,
		HireDate:          u.HireDate,
	}

	return user
}

// FromDomainEntity 从领域实体创建数据模型
func (u *UserModel) FromDomainEntity(user *entity.User) {
	u.ID = user.ID
	u.TenantID = user.TenantID
	u.InternalAppID = user.InternalAppID
	u.Username = user.Username
	u.Email = user.Email
	u.Phone = user.Phone
	u.RealName = user.RealName
	u.Avatar = user.Avatar
	u.Status = user.Status.String()
	u.IsSystem = Bool(user.IsSystem.ToBool())
	u.Password = user.Password.Hash()
	u.PasswordChangedAt = user.PasswordChangedAt // 新增字段
	u.LastLoginAt = user.LastLoginAt
	u.LastLoginIP = user.LastLoginIP
	u.LoginCount = user.LoginCount
	u.FailedCount = user.FailedCount
	u.LockedAt = user.LockedAt
	u.LockedUntil = user.LockedUntil
	u.LockReason = user.LockReason
	u.CreatedAt = user.CreatedAt
	u.UpdatedAt = user.UpdatedAt
	u.DeletedAt = user.DeletedAt
	u.DepartmentID = user.DepartmentID
	u.PositionID = user.PositionID
	u.EmployeeID = user.EmployeeID
	u.HireDate = user.HireDate
}

// NewUserModelFromDomain 从领域实体创建数据模型
func NewUserModelFromDomain(user *entity.User) *UserModel {
	model := &UserModel{}
	model.FromDomainEntity(user)
	return model
}
