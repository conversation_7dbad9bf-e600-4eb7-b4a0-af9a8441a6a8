{"permissions": {"allow": ["Bash(go mod:*)", "Bash(go build:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(rm:*)", "Bash(flutter analyze:*)", "Bash(flutter packages pub run build_runner build:*)", "Bash(flutter build:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(go clean:*)", "<PERSON><PERSON>(go test:*)", "Bash(git stash:*)", "Ba<PERSON>(go vet:*)", "Bash(npm start)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(touch:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(npx playwright install:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(chmod:*)", "Bash(APP_ENV=dev USE_NACOS=false go run cmd/main.go)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(USE_NACOS=false go run cmd/main.go)", "Bash(go list:*)", "Bash(find /Users/<USER>/personal/platforms/users -name \"*.go\" -exec grep -l \"EmailServiceConfig\" {} ;)", "Bash(./scripts/generate-proto.sh:*)", "Bash(curl -s http://localhost:8083/health)", "<PERSON><PERSON>(npx playwright:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_console_messages", "Bash(curl -s -X POST http://localhost:8084/api/user/auth/login )", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\"\"username\"\":\"\"<EMAIL>\"\",\"\"password\"\":\"\"admin123\"\"}')", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "Bash(curl -s -X POST http://localhost:8084/api/user/auth/register -H \"Content-Type: application/json\" -H \"X-Tenant-Code: prompts\" -d '{\n    \"\"\"\"username\"\"\"\": \"\"\"\"testuser\"\"\"\",\n    \"\"\"\"email\"\"\"\": \"\"\"\"<EMAIL>\"\"\"\",  \n    \"\"\"\"password\"\"\"\": \"\"\"\"Test123456\"\"\"\",\n    \"\"\"\"confirmPassword\"\"\"\": \"\"\"\"Test123456\"\"\"\"\n  }')", "WebFetch(domain:ant.design)", "Bash(find . -name \"*.go\" -exec grep -Hn \"fmt\\.Errorf\" {})", "Bash(find ./internal/domain -name \"*.go\" -exec grep -Hn \"fmt\\.Errorf\" {})", "Bash(cd /Users/<USER>/personal/platforms/auto-coding-system/practice/orchestrator)", "Bash(python3 system_orchestrator.py)", "<PERSON><PERSON>(python3 pm_agent.py)", "<PERSON><PERSON>(go run:*)", "Bash(npx tsc --noEmit --project tsconfig.json)", "Bash(npx tsc:*)", "WebFetch(domain:opentelemetry.io)", "Bash(./common-example)", "Bash(go fmt:*)", "Bash(npm run typecheck:*)", "Bash(npm run)", "Bash(go get:*)", "Bash(go get:*)", "Bash(./start.sh:*)", "Bash(./bin/email-system:*)", "Bash(./migration-check.sh:*)"], "deny": [], "defaultMode": "acceptEdits"}}