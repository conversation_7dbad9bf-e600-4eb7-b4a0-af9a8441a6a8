import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  login as apiLogin,
  logout as apiLogout,
  refreshToken as apiRefreshToken,
  LoginRequest,
  UserInfo,
  getCurrentUser,
  isLoggedIn,
  clearAuth,
  SecureTokenStorage,
} from '../services/auth';
import { useNavigate, useLocation } from 'react-router-dom';
import type { ApiResponse } from '../types/api';
import type { LoginResponse } from '../types';
import {SUCCESS} from "../constants/errorCodes";
import { LoginRedirect, handleUnauthorized, handleTokenExpired, handleLogout, handleForbidden } from '../utils/auth';
import { setGlobalForbiddenHandler } from '../utils/request';

// 全局导航处理器，用于在API拦截器中调用
let globalUnauthorizedHandler: (() => void) | null = null;

export const setGlobalUnauthorizedHandler = (handler: (() => void) | null) => {
  globalUnauthorizedHandler = handler;
};

export const getGlobalUnauthorizedHandler = () => {
  return globalUnauthorizedHandler;
};

interface AuthContextType {
  isAuthenticated: boolean;
  user: UserInfo | null;
  login: (credentials: LoginRequest, redirectTo?: string) => Promise<ApiResponse<LoginResponse>>;
  logout: () => void;
  loading: boolean;
  initialLoading: boolean;
  handleUnauthorized: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const navigate = useNavigate();
  const location = useLocation();

  // 检查认证状态的函数
  const checkAuthStatus = async () => {
    try {
      // 检查是否有有效的访问令牌
      if (isLoggedIn()) {
        // 获取用户信息
        const currentUser = getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
          setIsAuthenticated(true);
        } else {
          // 如果有token但没有用户信息，尝试重新获取
          await handleRefreshToken();
        }
      } else {
        // 没有有效token，检查是否有刷新令牌
        const refreshToken = SecureTokenStorage.getRefreshToken();
        if (refreshToken) {
          await handleRefreshToken();
        } else {
          // 没有任何认证信息
          setIsAuthenticated(false);
          setUser(null);
        }
      }
    } catch (error) {
      console.error('Failed to check auth status:', error);
      clearAuth();
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setInitialLoading(false);
    }
  };

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // 初始化统一的登录跳转系统
  useEffect(() => {
    LoginRedirect.setNavigate(navigate);
    LoginRedirect.setClearAuthCallback(clearAuth);
  }, [navigate]);

  // 设置全局未授权处理器
  useEffect(() => {
    setGlobalUnauthorizedHandler(handleUnauthorizedLocal);
    
    // 设置全局403权限不足处理器
    setGlobalForbiddenHandler((error) => {
      handleForbidden({
        message: '权限不足，无法访问该资源',
        error: error,
        showDetails: false
      });
    });
    
    // 清理函数
    return () => {
      setGlobalUnauthorizedHandler(null);
      setGlobalForbiddenHandler(null);
    };
  }, []);

  // 监听存储变化，处理多窗口同步
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      // 只处理当前域名下的存储变化
      if (e.key && e.newValue !== e.oldValue) {
        // 检查是否是认证相关的存储变化
        if (e.key.includes('token') || e.key.includes('user_info')) {
          console.log('Auth storage changed, rechecking auth status...');
          checkAuthStatus();
        }
      }
    };

    // 监听 localStorage 变化
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const handleRefreshToken = async () => {
    try {
      console.log('Attempting to refresh token...');
      const response = await apiRefreshToken();
      if (response) {
        console.log('Token refresh successful');
        setUser(response.user);
        setIsAuthenticated(true);
      } else {
        throw new Error('Failed to refresh token');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      
      // 使用统一的Token过期处理
      handleTokenExpired({
        message: 'Token已过期，请重新登录'
      });
      
      // 更新本地状态
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const login = async (credentials: LoginRequest, redirectTo?: string): Promise<ApiResponse<LoginResponse>> => {
    setLoading(true);
    try {
      const response = await apiLogin(credentials);
      if (response.code === SUCCESS) {
        setUser(response.data.user);
        setIsAuthenticated(true);
        SecureTokenStorage.setAccessToken(response.data.access_token, response.data.expires_in);
        SecureTokenStorage.setRefreshToken(response.data.refresh_token);
        SecureTokenStorage.setUserInfo(response.data.user);
        
        console.log('Login successful, user authenticated');
        
        // 成功登录后跳转到指定页面，默认为首页
        const targetPath = redirectTo || '/';
        setTimeout(() => navigate(targetPath, {replace: true}), 100);
      }
      return response;
    } catch (error) {
      console.error('Login failed:', error);
      clearAuth();
      setIsAuthenticated(false);
      setUser(null);
      throw error; // 将错误重新抛出，以便登录页面可以处理它
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      console.log('Logging out...');
      await apiLogout();
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // 更新本地状态
      setIsAuthenticated(false);
      setUser(null);
      setLoading(false);
      
      console.log('Logout completed');
      
      // 使用统一的退出登录处理
      handleLogout({
        message: '已成功退出登录'
      });
    }
  };

  // 处理未授权访问（401错误）的统一方法
  const handleUnauthorizedLocal = () => {
    console.log('Handling unauthorized access');
    
    // 更新本地状态
    setIsAuthenticated(false);
    setUser(null);
    
    // 使用统一的未授权处理
    handleUnauthorized({
      message: '登录已过期，请重新登录'
    });
  };

  const value = {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
    initialLoading,
    handleUnauthorized: handleUnauthorizedLocal,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 