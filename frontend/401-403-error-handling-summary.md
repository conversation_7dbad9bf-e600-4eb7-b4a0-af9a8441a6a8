# 401/403错误处理区分实施总结

## 🎯 问题解决
成功实现了401（未授权）和403（权限不足）错误的正确区分处理，避免了403错误误跳转到登录页面的问题。

## 📊 核心区别

| 错误类型 | HTTP状态码 | 场景 | 处理方式 | 用户体验 |
|---------|-----------|------|----------|----------|
| **401 未授权** | 401 | 用户未登录或Token过期 | 清除认证信息 + 跳转登录页 | 需要重新登录 |
| **403 权限不足** | 403 | 用户已登录但权限不足 | 只显示错误提示，不跳转 | 知道已登录，需联系管理员 |

## ✅ 实施完成

### 1. HTTP拦截器更新 (`src/utils/request.ts`)
```typescript
// 401错误 - 跳转登录页
if (error.response?.status === HTTP_STATUS.UNAUTHORIZED) {
    const unauthorizedHandler = getGlobalUnauthorizedHandler();
    if (unauthorizedHandler) {
        unauthorizedHandler(); // 自动跳转登录页
    }
}

// 403错误 - 只提示不跳转  
if (error.response?.status === HTTP_STATUS.FORBIDDEN) {
    const forbiddenHandler = getGlobalForbiddenHandler();
    if (forbiddenHandler) {
        forbiddenHandler(error); // 只显示错误提示
    }
}
```

### 2. 统一认证工具类扩展 (`src/utils/auth.ts`)
- ✅ 新增 `handleForbidden()` 方法处理403错误
- ✅ 新增 `ForbiddenOptions` 接口
- ✅ 新增 `LoginRedirect.handleForbidden()` 静态方法

### 3. React Hook集成 (`src/hooks/useAuthRedirect.ts`)
```typescript
const {
  onUnauthorized,    // 401: 跳转登录页
  onForbidden,       // 403: 只显示提示，不跳转
  // ... 其他方法
} = useAuthRedirect();
```

### 4. AuthContext集成 (`src/contexts/AuthContext.tsx`)
- ✅ 设置全局403错误处理器
- ✅ 自动集成到HTTP拦截器

### 5. 示例和测试组件
- ✅ `AuthRedirectExample.tsx` - 展示正确使用方法
- ✅ `ErrorHandlingTest.tsx` - 验证401/403处理逻辑

## 🚀 使用方法

### 在组件中正确处理错误
```typescript
const { onUnauthorized, onForbidden } = useAuthRedirect();

try {
  await protectedApiCall();
} catch (error: any) {
  if (error.response?.status === 401) {
    // 未授权，需要重新登录
    onUnauthorized({ message: '登录已过期，请重新登录' });
  } else if (error.response?.status === 403) {
    // 权限不足，不跳转登录页
    onForbidden({ 
      message: '您没有权限执行此操作，请联系管理员',
      error: error 
    });
  }
}
```

### 自动处理（HTTP拦截器）
所有401和403错误会被自动拦截和处理：
- **401错误**：自动清除认证信息并跳转登录页
- **403错误**：自动显示权限不足提示，不跳转登录页

## 🔧 API参考

### 新增接口

#### ForbiddenOptions
```typescript
interface ForbiddenOptions {
  message?: string;        // 自定义提示消息
  error?: any;            // 原始错误对象
  showDetails?: boolean;   // 是否显示详细错误信息
}
```

#### 新增方法
```typescript
// 处理403权限不足
handleForbidden(options?: ForbiddenOptions): void

// Hook中的便捷方法
const { onForbidden } = useAuthRedirect();
onForbidden({ message: '自定义权限不足提示' });
```

## 🎉 效果对比

### 修复前 ❌
- 401和403错误都跳转到登录页
- 用户权限不足时被强制退出登录
- 用户体验混乱，不知道是认证问题还是权限问题

### 修复后 ✅  
- **401错误**：正确跳转登录页，用户知道需要重新登录
- **403错误**：只显示权限提示，用户知道已登录但权限不足
- 用户体验清晰，能准确理解问题性质并采取正确行动

## 📝 最佳实践

1. **自动处理优先**：大部分情况下依靠HTTP拦截器自动处理
2. **业务定制**：需要特殊提示时使用Hook手动调用
3. **错误分类**：明确区分认证问题（401）和权限问题（403）
4. **用户引导**：为403错误提供明确的解决方案指引

## 🔄 向后兼容

- 所有现有的401错误处理逻辑保持不变
- 新增的403处理不影响原有功能
- Hook接口向后兼容，只是新增了`onForbidden`方法

---

**现在系统能够正确区分401和403错误，只有401错误才会跳转到登录页面，403错误只会显示权限不足的提示！** 🎊